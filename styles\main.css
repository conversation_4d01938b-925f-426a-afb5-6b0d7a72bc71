/* CSS Custom Properties */
:root {
    /* Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-light: #94a3b8;
    --background-primary: #ffffff;
    --background-secondary: #f8fafc;
    --background-dark: #0f172a;
    --border-color: #e2e8f0;
    --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, Blink<PERSON>ac<PERSON>ys<PERSON><PERSON><PERSON>, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Layout */
    --container-max-width: 1200px;
    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --navbar-height: 70px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

/* Utility Classes */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.section-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    margin-bottom: 0;
}

.highlight {
    color: var(--primary-color);
    position: relative;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 1px;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
    color: white;
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-full {
    width: 100%;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition);
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-medium);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--navbar-height);
}

.nav-logo a {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
}

.nav-link {
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    position: relative;
    padding: var(--spacing-sm) 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    transition: var(--transition);
    border-radius: 2px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--navbar-height) 0 var(--spacing-3xl);
    background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
    position: relative;
}

.hero-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.hero-content {
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--font-size-2xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    min-height: 2.5rem;
    display: flex;
    align-items: center;
}

.cursor {
    animation: blink 1s infinite;
    color: var(--primary-color);
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    max-width: 500px;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInRight 1s ease-out 0.3s both;
}

.hero-avatar {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-large);
    position: relative;
}

.hero-avatar::before {
    content: '';
    position: absolute;
    inset: -10px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    z-index: -1;
    opacity: 0.3;
    animation: pulse 2s ease-in-out infinite;
}

.avatar-placeholder {
    width: 280px;
    height: 280px;
    border-radius: 50%;
    background-color: var(--background-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: var(--text-light);
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    font-size: var(--font-size-xl);
    color: var(--text-light);
}

/* About Section */
.about {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-secondary);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.about-paragraph {
    font-size: var(--font-size-lg);
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-top: var(--spacing-2xl);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-lg);
    background-color: var(--background-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.about-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.about-img-container {
    width: 400px;
    height: 300px;
    border-radius: var(--border-radius-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-large);
    position: relative;
    overflow: hidden;
}

.img-placeholder {
    font-size: 4rem;
    color: white;
    opacity: 0.8;
}

/* Skills Section */
.skills {
    padding: var(--spacing-3xl) 0;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
}

.skill-category {
    background-color: var(--background-primary);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
}

.skill-category:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-large);
}

.category-title {
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    text-align: center;
    font-size: var(--font-size-xl);
}

.skill-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.skill-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-lg);
    background-color: var(--background-secondary);
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.skill-item:hover {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.skill-item i {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    transition: var(--transition);
}

.skill-item:hover i {
    color: white;
}

.skill-item span {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-align: center;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.1;
    }
    100% {
        transform: scale(1);
        opacity: 0.3;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --font-size-5xl: 2.5rem;
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.5rem;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: var(--navbar-height);
        flex-direction: column;
        background-color: var(--background-primary);
        width: 100%;
        text-align: center;
        transition: var(--transition);
        box-shadow: var(--shadow-medium);
        padding: var(--spacing-lg) 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }
    
    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-2xl);
    }
    
    .hero-avatar {
        width: 250px;
        height: 250px;
    }
    
    .avatar-placeholder {
        width: 230px;
        height: 230px;
        font-size: 3rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
    
    .about-stats {
        grid-template-columns: 1fr;
    }
    
    .skills-grid {
        grid-template-columns: 1fr;
    }
    
    .skill-items {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Projects Section */
.projects {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-secondary);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.project-card {
    background-color: var(--background-primary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
    cursor: pointer;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-large);
}

.project-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-img-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    opacity: 0.9;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-links {
    display: flex;
    gap: var(--spacing-lg);
}

.project-link {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    transition: var(--transition);
}

.project-link:hover {
    background-color: var(--accent-color);
    transform: scale(1.1);
    color: white;
}

.project-content {
    padding: var(--spacing-xl);
}

.project-title {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-xl);
}

.project-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.tech-tag {
    background-color: var(--background-secondary);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    border: 1px solid var(--border-color);
}

/* Contact Section */
.contact {
    padding: var(--spacing-3xl) 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: start;
}

.contact-info h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-2xl);
}

.contact-info p {
    font-size: var(--font-size-lg);
    line-height: 1.7;
    margin-bottom: var(--spacing-2xl);
}

.contact-details {
    margin-bottom: var(--spacing-2xl);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
}

.contact-item i {
    width: 20px;
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    width: 50px;
    height: 50px;
    background-color: var(--background-secondary);
    color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    transition: var(--transition);
    border: 2px solid var(--border-color);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

/* Form Styles */
.form {
    background-color: var(--background-primary);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-medium);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    transition: var(--transition);
    background-color: var(--background-primary);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.error-message {
    display: block;
    color: #ef4444;
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    opacity: 0;
    transition: var(--transition);
}

.error-message.show {
    opacity: 1;
}

.btn-loading {
    display: none;
}

.btn.loading .btn-text {
    display: none;
}

.btn.loading .btn-loading {
    display: inline;
}

/* Footer */
.footer {
    background-color: var(--background-dark);
    color: var(--text-light);
    padding: var(--spacing-2xl) 0;
    text-align: center;
}

.footer-content p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-light);
}

.footer-content p:last-child {
    margin-bottom: 0;
    opacity: 0.8;
}

/* Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .projects-grid {
        grid-template-columns: 1fr;
    }

    .project-card {
        max-width: 400px;
        margin: 0 auto;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .social-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
    }

    .skill-items {
        grid-template-columns: 1fr;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .project-links {
        gap: var(--spacing-md);
    }

    .project-link {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }

    .form {
        padding: var(--spacing-lg);
    }
}

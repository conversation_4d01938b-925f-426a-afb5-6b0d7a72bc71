<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Pradeep Yadav - Fullstack Developer Portfolio. Modern web development solutions with expertise in frontend and backend technologies.">
    <meta name="keywords" content="fullstack developer, web development, portfolio, pradeep yadav">
    <meta name="author" content="Pradeep Yadav">
    
    <title>Pradeep Yadav | Fullstack Developer</title>
    
    <!-- Preconnect to Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Main stylesheet -->
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="#home">pradeepyadav.space</a>
            </div>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="#skills" class="nav-link">Skills</a>
                </li>
                <li class="nav-item">
                    <a href="#projects" class="nav-link">Projects</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="nav-toggle" id="mobile-menu">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Hi, I'm <span class="highlight">Pradeep Yadav</span>
                </h1>
                <p class="hero-subtitle">
                    <span id="typed-text"></span>
                    <span class="cursor">|</span>
                </p>
                <p class="hero-description">
                    Passionate fullstack developer crafting modern web experiences with clean code and innovative solutions.
                </p>
                <div class="hero-buttons">
                    <a href="#projects" class="btn btn-primary">View My Work</a>
                    <a href="#contact" class="btn btn-secondary">Get In Touch</a>
                </div>
            </div>
            <div class="hero-image">
                <div class="hero-avatar">
                    <div class="avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="scroll-indicator">
            <div class="scroll-arrow">
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">About Me</h2>
                <p class="section-subtitle">Get to know me better</p>
            </div>
            <div class="about-content">
                <div class="about-text">
                    <p class="about-paragraph">
                        I'm a passionate fullstack developer with a love for creating elegant, efficient, and user-friendly web applications. 
                        With expertise spanning both frontend and backend technologies, I bring ideas to life through clean code and thoughtful design.
                    </p>
                    <p class="about-paragraph">
                        My journey in web development has equipped me with a diverse skill set, allowing me to tackle complex challenges 
                        and deliver comprehensive solutions. I believe in continuous learning and staying updated with the latest technologies 
                        to provide the best possible outcomes for every project.
                    </p>
                    <div class="about-stats">
                        <div class="stat-item">
                            <span class="stat-number">50+</span>
                            <span class="stat-label">Projects Completed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">3+</span>
                            <span class="stat-label">Years Experience</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Client Satisfaction</span>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <div class="about-img-container">
                        <div class="img-placeholder">
                            <i class="fas fa-code"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Skills & Technologies</h2>
                <p class="section-subtitle">Technologies I work with</p>
            </div>
            <div class="skills-grid">
                <div class="skill-category">
                    <h3 class="category-title">Frontend</h3>
                    <div class="skill-items">
                        <div class="skill-item">
                            <i class="fab fa-html5"></i>
                            <span>HTML5</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-css3-alt"></i>
                            <span>CSS3</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-js-square"></i>
                            <span>JavaScript</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-react"></i>
                            <span>React</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-vue"></i>
                            <span>Vue.js</span>
                        </div>
                    </div>
                </div>
                <div class="skill-category">
                    <h3 class="category-title">Backend</h3>
                    <div class="skill-items">
                        <div class="skill-item">
                            <i class="fab fa-node-js"></i>
                            <span>Node.js</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-python"></i>
                            <span>Python</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-database"></i>
                            <span>MongoDB</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-server"></i>
                            <span>Express.js</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-cloud"></i>
                            <span>AWS</span>
                        </div>
                    </div>
                </div>
                <div class="skill-category">
                    <h3 class="category-title">Tools & Others</h3>
                    <div class="skill-items">
                        <div class="skill-item">
                            <i class="fab fa-git-alt"></i>
                            <span>Git</span>
                        </div>
                        <div class="skill-item">
                            <i class="fab fa-docker"></i>
                            <span>Docker</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-mobile-alt"></i>
                            <span>Responsive Design</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-paint-brush"></i>
                            <span>UI/UX Design</span>
                        </div>
                        <div class="skill-item">
                            <i class="fas fa-rocket"></i>
                            <span>Performance Optimization</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Featured Projects</h2>
                <p class="section-subtitle">Some of my recent work</p>
            </div>
            <div class="projects-grid">
                <div class="project-card" data-aos="fade-up">
                    <div class="project-image">
                        <div class="project-img-placeholder">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link" aria-label="View live demo">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="project-link" aria-label="View source code">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">E-Commerce Platform</h3>
                        <p class="project-description">
                            A full-featured e-commerce platform built with React and Node.js, featuring user authentication, 
                            payment integration, and admin dashboard.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Node.js</span>
                            <span class="tech-tag">MongoDB</span>
                            <span class="tech-tag">Stripe</span>
                        </div>
                    </div>
                </div>

                <div class="project-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="project-image">
                        <div class="project-img-placeholder">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link" aria-label="View live demo">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="project-link" aria-label="View source code">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">Task Management App</h3>
                        <p class="project-description">
                            A collaborative task management application with real-time updates, drag-and-drop functionality, 
                            and team collaboration features.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">Vue.js</span>
                            <span class="tech-tag">Express</span>
                            <span class="tech-tag">Socket.io</span>
                            <span class="tech-tag">PostgreSQL</span>
                        </div>
                    </div>
                </div>

                <div class="project-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="project-image">
                        <div class="project-img-placeholder">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="project-overlay">
                            <div class="project-links">
                                <a href="#" class="project-link" aria-label="View live demo">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="#" class="project-link" aria-label="View source code">
                                    <i class="fab fa-github"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <h3 class="project-title">Analytics Dashboard</h3>
                        <p class="project-description">
                            A comprehensive analytics dashboard with interactive charts, real-time data visualization, 
                            and customizable reporting features.
                        </p>
                        <div class="project-tech">
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">D3.js</span>
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">FastAPI</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Get In Touch</h2>
                <p class="section-subtitle">Let's work together</p>
            </div>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Let's discuss your project</h3>
                    <p>
                        I'm always interested in new opportunities and exciting projects. 
                        Whether you have a question or just want to say hi, feel free to reach out!
                    </p>
                    <div class="contact-details">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-globe"></i>
                            <span>pradeepyadav.space</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Available for remote work</span>
                        </div>
                    </div>
                    <div class="social-links">
                        <a href="#" class="social-link" aria-label="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="social-link" aria-label="LinkedIn">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="social-link" aria-label="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link" aria-label="Email">
                            <i class="fas fa-envelope"></i>
                        </a>
                    </div>
                </div>
                <div class="contact-form">
                    <form id="contact-form" class="form">
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" id="name" name="name" required>
                            <span class="error-message" id="name-error"></span>
                        </div>
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" required>
                            <span class="error-message" id="email-error"></span>
                        </div>
                        <div class="form-group">
                            <label for="subject">Subject</label>
                            <input type="text" id="subject" name="subject" required>
                            <span class="error-message" id="subject-error"></span>
                        </div>
                        <div class="form-group">
                            <label for="message">Message</label>
                            <textarea id="message" name="message" rows="5" required></textarea>
                            <span class="error-message" id="message-error"></span>
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">
                            <span class="btn-text">Send Message</span>
                            <span class="btn-loading">Sending...</span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2024 Pradeep Yadav. All rights reserved.</p>
                <p>Built with ❤️ using vanilla HTML, CSS, and JavaScript</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="scripts/main.js"></script>
</body>
</html>

# Modern Minimalist Portfolio Website

A clean, professional portfolio website for Pradeep Yadav - Fullstack Developer, built with vanilla HTML, CSS, and JavaScript.

## 🌟 Features

- **Modern Minimalist Design**: Clean, contemporary aesthetic with professional color scheme
- **Fully Responsive**: Optimized for desktop, tablet, and mobile devices
- **Interactive Elements**: Smooth animations, typing effects, and scroll-based animations
- **Performance Optimized**: Fast loading with vanilla JavaScript (no frameworks)
- **Accessibility Focused**: Semantic HTML and proper ARIA labels
- **Contact Form**: Functional contact form with validation
- **Smooth Scrolling**: Enhanced navigation experience

## 🚀 Sections

1. **Hero Section**: Dynamic typing effect showcasing roles and skills
2. **About**: Professional summary with animated statistics
3. **Skills**: Technology stack organized by categories (Frontend, Backend, Tools)
4. **Projects**: Featured portfolio projects with hover effects
5. **Contact**: Contact form and social media links

## 🛠️ Technologies Used

- **HTML5**: Semantic markup for accessibility
- **CSS3**: Modern features including:
  - CSS Grid and Flexbox for layouts
  - CSS Custom Properties (variables)
  - Smooth animations and transitions
  - Mobile-first responsive design
- **JavaScript (ES6+)**: Interactive functionality including:
  - Intersection Observer for scroll animations
  - Form validation
  - Mobile menu toggle
  - Typing effect animation

## 📁 Project Structure

```
nextportfolio/
├── index.html              # Main HTML file
├── styles/
│   └── main.css            # Main stylesheet
├── scripts/
│   └── main.js             # JavaScript functionality
├── assets/                 # Images and other assets (placeholder)
└── README.md              # Project documentation
```

## 🎨 Design Features

- **Color Scheme**: Professional blue and gray palette with accent colors
- **Typography**: Inter font family for modern, readable text
- **Animations**: Subtle hover effects and scroll-based animations
- **Icons**: Font Awesome icons for visual enhancement
- **Layout**: CSS Grid and Flexbox for responsive layouts

## 📱 Responsive Design

The website is fully responsive with breakpoints at:
- Desktop: 1200px and above
- Tablet: 768px - 1199px
- Mobile: 480px - 767px
- Small Mobile: Below 480px

## 🔧 Customization

### Colors
Update CSS custom properties in `:root` selector in `styles/main.css`:

```css
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    /* ... other colors */
}
```

### Content
- Update personal information in `index.html`
- Modify project details in the projects section
- Update social media links in the contact section
- Replace placeholder content with actual information

### Styling
- Modify spacing using CSS custom properties
- Adjust animations in the CSS file
- Update typography by changing font imports and variables

## 🚀 Getting Started

1. **Clone or download** the project files
2. **Open `index.html`** in a web browser
3. **Customize content** with your personal information
4. **Add your projects** and update project links
5. **Replace placeholder images** in the assets folder
6. **Update social media links** and contact information

## 📧 Contact Form

The contact form includes:
- Real-time validation
- Error messaging
- Loading states
- Success notifications

Note: The form currently shows a success message without actually sending emails. To make it functional, you'll need to:
- Add a backend service (Node.js, PHP, etc.)
- Use a service like Formspree, Netlify Forms, or EmailJS
- Update the form action and method accordingly

## 🌐 Domain Integration

The website is designed with "pradeepyadav.space" domain integration:
- Navigation logo displays the domain
- Contact section includes the domain
- Email addresses use the domain format

## 📈 Performance

- **Vanilla JavaScript**: No framework dependencies for faster loading
- **Optimized CSS**: Efficient selectors and minimal redundancy
- **Font optimization**: Preconnect to Google Fonts for faster loading
- **Image optimization**: Placeholder system ready for optimized images

## 🔍 SEO Ready

- Semantic HTML structure
- Meta tags for description and keywords
- Proper heading hierarchy
- Alt text ready for images
- Clean URL structure

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Feel free to fork this project and customize it for your own portfolio. If you make improvements that could benefit others, pull requests are welcome!

## 📞 Support

For questions or support, please contact:
- Email: <EMAIL>
- Website: pradeepyadav.space

---

Built with ❤️ using vanilla HTML, CSS, and JavaScript
